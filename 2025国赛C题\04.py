import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
import torch.optim as optim

np.random.seed(42)
torch.manual_seed(42)

class MLP(nn.Module):
    def __init__(self, input_size):
        super(MLP, self).__init__()
        self.fc1 = nn.Linear(input_size, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        return x

def load_data():
    # 加载男胎和女胎数据
    male_df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    female_df = pd.read_excel(r'D:\数学建模\2025国赛C题\女胎数据.xlsx')

    # 添加性别标签：1=男胎，0=女胎
    male_df['gender'] = 1
    female_df['gender'] = 0

    return male_df, female_df

def prepare_features(male_df, female_df):
    # 指定的特征列表
    features = [
        'GC含量', '13号染色体的Z值', '18号染色体的Z值', '21号染色体的Z值',
        'X染色体的Z值', 'X染色体浓度', '13号染色体的GC含量', '18号染色体的GC含量',
        '21号染色体的GC含量', '被过滤掉读段数的比例', '染色体的非整倍体',
        '原始读段数', '在参考基因组上比对的比例', '重复读段的比例', '唯一比对的读段数'
    ]

    # 处理列名差异（男胎数据中有些列名后面有空格）
    male_features = []
    female_features = []

    for feature in features:
        # 在男胎数据中查找匹配的列名
        male_col = None
        for col in male_df.columns:
            if feature in col or col.strip() == feature:
                male_col = col
                break
        if male_col:
            male_features.append(male_col)

        # 在女胎数据中查找匹配的列名
        female_col = None
        for col in female_df.columns:
            if feature in col or col.strip() == feature:
                female_col = col
                break
        if female_col:
            female_features.append(female_col)

    # 取交集，确保两个数据集都有的特征
    common_features = []
    for i, feature in enumerate(features):
        if i < len(male_features) and i < len(female_features):
            common_features.append((male_features[i], female_features[i], feature))

    return common_features

def train_model(male_df, female_df, common_features):
    # 从女胎数据中分出80%训练，20%测试
    female_train, female_test = train_test_split(female_df, test_size=0.2, random_state=42)

    # 构建训练集：全部男胎 + 80%女胎
    train_data = []
    train_labels = []

    # 添加所有男胎数据
    for _, row in male_df.iterrows():
        features = []
        for male_col, _, _ in common_features:
            features.append(row[male_col])
        train_data.append(features)
        train_labels.append(1)  # 男胎标签为1

    # 添加80%女胎数据
    for _, row in female_train.iterrows():
        features = []
        for _, female_col, _ in common_features:
            features.append(row[female_col])
        train_data.append(features)
        train_labels.append(0)  # 女胎标签为0

    # 构建测试集：20%女胎
    test_data = []
    test_labels = []
    for _, row in female_test.iterrows():
        features = []
        for _, female_col, _ in common_features:
            features.append(row[female_col])
        test_data.append(features)
        test_labels.append(0)  # 女胎标签为0

    # 转换为DataFrame并处理缺失值和非数值数据
    X_train = pd.DataFrame(train_data)
    X_test = pd.DataFrame(test_data)

    # 将非数值数据转换为数值，无法转换的设为NaN
    X_train = X_train.apply(pd.to_numeric, errors='coerce')
    X_test = X_test.apply(pd.to_numeric, errors='coerce')

    # 用中位数填充缺失值
    for col in X_train.columns:
        median_val = X_train[col].median()
        if pd.isna(median_val):  # 如果中位数也是NaN，用0填充
            median_val = 0
        X_train[col] = X_train[col].fillna(median_val)
        X_test[col] = X_test[col].fillna(median_val)

    y_train = np.array(train_labels)
    y_test = np.array(test_labels)

    # 检查并处理无穷大值
    X_train = X_train.replace([np.inf, -np.inf], np.nan)
    X_test = X_test.replace([np.inf, -np.inf], np.nan)

    # 再次填充可能的NaN值
    X_train = X_train.fillna(0)
    X_test = X_test.fillna(0)

    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # 检查标准化后的数据
    X_train_scaled = np.nan_to_num(X_train_scaled, nan=0.0, posinf=1.0, neginf=-1.0)
    X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0, posinf=1.0, neginf=-1.0)

    # 转换为张量
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    y_train_tensor = torch.FloatTensor(y_train).reshape(-1, 1)
    X_test_tensor = torch.FloatTensor(X_test_scaled)
    y_test_tensor = torch.FloatTensor(y_test).reshape(-1, 1)

    # 初始化模型
    model = MLP(X_train_scaled.shape[1])
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    print(f"训练集: {len(X_train)} 样本 (男胎: {sum(y_train)}, 女胎: {len(y_train)-sum(y_train)})")
    print(f"测试集: {len(X_test)} 样本 (全部为女胎)")
    print(f"使用特征数: {len(common_features)}")

    # 训练
    for epoch in range(100):
        model.train()
        optimizer.zero_grad()
        outputs = model(X_train_tensor)
        loss = criterion(outputs, y_train_tensor)
        loss.backward()
        optimizer.step()
        if epoch % 20 == 0:
            print(f'Epoch {epoch}, Loss: {loss.item():.4f}')

    # 测试
    model.eval()
    with torch.no_grad():
        test_outputs = model(X_test_tensor)
        test_pred = (test_outputs > 0.5).float()
        accuracy = (test_pred == y_test_tensor).float().mean()

    print(f'测试准确率: {accuracy:.4f}')
    return model

def main():
    male_df, female_df = load_data()
    common_features = prepare_features(male_df, female_df)
    model = train_model(male_df, female_df, common_features)

if __name__ == "__main__":
    main()




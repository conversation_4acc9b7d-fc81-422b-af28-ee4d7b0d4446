import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
import torch.optim as optim

np.random.seed(42)
torch.manual_seed(42)

class MLP(nn.Module):
    def __init__(self, input_size):
        super(MLP, self).__init__()
        self.fc1 = nn.Linear(input_size, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        return x

def load_data():
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\女胎数据.xlsx')

    # 根据染色体Z值定义异常
    df['label'] = 0
    z_cols = ['13号染色体的Z值', '18号染色体的Z值', '21号染色体的Z值']
    for col in z_cols:
        if col in df.columns:
            df.loc[abs(df[col]) > 2.5, 'label'] = 1

    return df

def prepare_features(df):
    features = ['检测孕周', '孕妇BMI', 'X染色体浓度', 'GC含量', '原始读段数']
    X = df[features].fillna(df[features].median())
    y = df['label']
    return X, y

def train_model(X, y):
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)

    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    X_train_tensor = torch.FloatTensor(X_train_scaled)
    y_train_tensor = torch.FloatTensor(y_train.values).reshape(-1, 1)
    X_test_tensor = torch.FloatTensor(X_test_scaled)
    y_test_tensor = torch.FloatTensor(y_test.values).reshape(-1, 1)

    model = MLP(X_train_scaled.shape[1])
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    # 训练
    for epoch in range(100):
        model.train()
        optimizer.zero_grad()
        outputs = model(X_train_tensor)
        loss = criterion(outputs, y_train_tensor)
        loss.backward()
        optimizer.step()
        

    # 测试
    model.eval()
    with torch.no_grad():
        test_outputs = model(X_test_tensor)
        test_pred = (test_outputs > 0.5).float()
        accuracy = (test_pred == y_test_tensor).float().mean()

    print(f'测试准确率: {accuracy:.4f}')
    return model

def main():
    df = load_data()
    X, y = prepare_features(df)
    model = train_model(X, y)

if __name__ == "__main__":
    main()




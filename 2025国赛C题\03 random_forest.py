# 第三问：使用随机森林算法预测Y染色体浓度检测结果
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score, r2_score, mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 随机森林分类
def random_forest_classification(df):
    # 数据预处理
    df_clean = df.dropna(subset=['检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

    # 特征选择
    features = ['检测孕周', '孕妇BMI', '年龄', 'GC含量', '原始读段数']
    X = df_clean[features]
    y = (df_clean['Y染色体浓度'] > 0.04).astype(int)  # 目标变量：是否达标

    # 数据分割和模型训练
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    rf_clf = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)
    rf_clf.fit(X_train, y_train)

    # 预测和评估
    y_pred = rf_clf.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)

    print("\n随机森林分类结果：")
    print(f"准确率: {accuracy:.4f}")
    print(classification_report(y_test, y_pred))

    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': features,
        'importance': rf_clf.feature_importances_
    }).sort_values('importance', ascending=False)
    print("\n特征重要性：")
    print(feature_importance)

    # 可视化特征重要性
    plt.figure(figsize=(8, 5))
    sns.barplot(data=feature_importance, x='importance', y='feature')
    plt.title('随机森林分类特征重要性')
    plt.xlabel('重要性')
    plt.tight_layout()
    plt.show()

    return rf_clf

# 随机森林回归
def random_forest_regression(df):
    # 数据预处理
    df_clean = df.dropna(subset=['检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

    # 特征选择
    features = ['检测孕周', '孕妇BMI', '年龄', 'GC含量', '原始读段数']
    X = df_clean[features]
    y = df_clean['Y染色体浓度']

    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

    # 随机森林回归器
    rf_reg = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
    rf_reg.fit(X_train, y_train)

    # 预测
    y_pred = rf_reg.predict(X_test)

    # 评估
    r2 = r2_score(y_test, y_pred)
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)

    print("\n随机森林回归结果：")
    print(f"R²: {r2:.4f}")
    print(f"RMSE: {rmse:.4f}")

    # 预测vs实际值图
    plt.figure(figsize=(8, 6))
    plt.scatter(y_test, y_pred, alpha=0.6)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], c='r', lw=2)
    plt.xlabel('实际Y染色体浓度')
    plt.ylabel('预测Y染色体浓度')
    plt.title(f'随机森林回归预测结果 (R² = {r2:.4f})')
    plt.tight_layout()
    plt.show()

    return rf_reg

# BMI分组最佳检测时间
def bmi_group_optimal_time(df):
    # 数据预处理
    df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

    # 定义BMI分组（按照题目要求）
    def get_bmi_group(bmi):
        if 20 <= bmi < 28:
            return '[20,28)'
        elif 28 <= bmi < 32:
            return '[28,32)'
        elif 32 <= bmi < 36:
            return '[32,36)'
        elif 36 <= bmi < 40:
            return '[36,40)'
        elif bmi >= 40:
            return '≥40'
        else:
            return '<20'  # BMI小于20的情况

    df_clean = df_clean.copy()
    df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)

    # 为每个孕妇找到首次达标时间
    individual_data = []
    for _, group in df_clean.groupby('孕妇代码'):
        group = group.sort_values('检测孕周')
        mask = group['Y染色体浓度'] > 0.04
        if mask.any():
            optimal_time = group[mask]['检测孕周'].iloc[0]
            达标状态 = 1
        else:
            optimal_time = group['检测孕周'].iloc[-1]
            达标状态 = 0

        info = group.iloc[0]
        individual_data.append({
            '孕妇BMI': info['孕妇BMI'],
            'BMI组': info['BMI组'],
            '年龄': info['年龄'],
            'GC含量': info['GC含量'],
            '检测时间': optimal_time,
            '达标状态': 达标状态
        })

    individual_df = pd.DataFrame(individual_data)

    print("\n BMI分组统计：")
    for group_name in individual_df['BMI组'].unique():
        group_data = individual_df[individual_df['BMI组'] == group_name]
        print(f"\n{group_name}组:")
        print(f"  样本数: {len(group_data)}")
        print(f"  达标率: {group_data['达标状态'].mean():.2%}")
        print(f"  平均检测时间: {group_data['检测时间'].mean():.1f}周")

    # 使用随机森林为每个BMI组找最佳时间
    optimal_times_by_group = {}

    for group_name in individual_df['BMI组'].unique():
        group_data = individual_df[individual_df['BMI组'] == group_name]

        # 特征：年龄、GC含量
        features = ['年龄', 'GC含量']
        X = group_data[features]
        y = group_data['检测时间']

        if len(group_data) > 10:  # 确保有足够样本
            # 随机森林回归
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)

            # 预测该组的最佳时间（使用组内平均特征）
            avg_features = X.mean().values.reshape(1, -1)
            predicted_time = rf.predict(avg_features)[0]

            # 考虑达标率调整时间
            success_rate = group_data['达标状态'].mean()
            if success_rate < 0.8:  # 达标率低，建议稍晚检测
                predicted_time += 1
            elif success_rate > 0.95:  # 达标率很高，可以稍早检测
                predicted_time -= 0.5

            optimal_times_by_group[group_name] = {
                '建议检测时间': round(predicted_time, 1),
                '样本数': len(group_data),
                '达标率': f"{success_rate:.2%}",
                '特征重要性': dict(zip(features, rf.feature_importances_))
            }
        else:
            # 样本太少，使用平均值
            optimal_times_by_group[group_name] = {
                '建议检测时间': round(group_data['检测时间'].mean(), 1),
                '样本数': len(group_data),
                '达标率': f"{group_data['达标状态'].mean():.2%}",
                '特征重要性': '样本不足'
            }

    print("\n各BMI组最佳检测时间建议：")
    for group, info in optimal_times_by_group.items():
        print(f"\n{group}:")
        print(f"  建议检测时间: {info['建议检测时间']}周")
        print(f"  基于样本数: {info['样本数']}")
        print(f"  该组达标率: {info['达标率']}")
        if isinstance(info['特征重要性'], dict):
            print(f"  年龄重要性: {info['特征重要性']['年龄']:.3f}")
            print(f"  GC含量重要性: {info['特征重要性']['GC含量']:.3f}")

    return optimal_times_by_group, individual_df

def main():
    # 加载数据
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    print(f"数据形状: {df.shape}")

    # 1. 分类任务：预测Y染色体浓度是否达标
    random_forest_classification(df)

    # 2. 回归任务：预测Y染色体浓度
    random_forest_regression(df)

    # 3. BMI分组最佳检测时间分析
    optimal_times, _ = bmi_group_optimal_time(df)

    # 总结建议
    print("基于随机森林的BMI分组最佳检测时间")

    # 按BMI从小到大排序输出
    bmi_order = ['[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']
    for group in bmi_order:
        if group in optimal_times:
            info = optimal_times[group]
            print(f"BMI {group}: {info['建议检测时间']}周 (样本数:{info['样本数']}, 达标率:{info['达标率']})")


# 误差敏感性分析
def error_sensitivity_analysis(df):
    print("\n检测误差敏感性分析：")

    # 定义不同的误差水平
    error_levels = [0.001, 0.005, 0.01, 0.02, 0.05]  # Y染色体浓度的误差范围
    results = {}

    # 获取基准结果（无误差）
    baseline_results, _ = bmi_group_optimal_time(df)

    for error_level in error_levels:
        print(f"\n误差水平: ±{error_level:.3f}")

        # 蒙特卡洛模拟
        simulation_results = []
        for _ in range(50):  # 50次模拟
            # 添加随机误差
            df_error = df.copy()
            noise = np.random.normal(0, error_level, len(df_error))
            df_error['Y染色体浓度'] = df_error['Y染色体浓度'] + noise
            df_error['Y染色体浓度'] = np.maximum(df_error['Y染色体浓度'], 0)  # 确保非负

            # 重新分析
            try:
                error_results, _ = bmi_group_optimal_time(df_error)
                simulation_results.append(error_results)
            except:
                continue

        # 统计结果
        if simulation_results:
            avg_results = {}
            for bmi_group in ['[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']:
                times = []
                for result in simulation_results:
                    if bmi_group in result:
                        times.append(result[bmi_group]['建议检测时间'])

                if times:
                    avg_time = np.mean(times)
                    std_time = np.std(times)
                    baseline_time = baseline_results.get(bmi_group, {}).get('建议检测时间', 0)

                    avg_results[bmi_group] = {
                        '平均时间': round(avg_time, 1),
                        '标准差': round(std_time, 2),
                        '与基准差异': round(avg_time - baseline_time, 2)
                    }

            results[error_level] = avg_results

    # 输出结果
    print("\n误差影响汇总：")
    print("BMI组\t\t误差水平\t平均时间\t标准差\t与基准差异")

    for bmi_group in ['[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']:
        baseline_time = baseline_results.get(bmi_group, {}).get('建议检测时间', 0)
        print(f"{bmi_group}\t基准\t\t{baseline_time}周\t\t-\t\t-")

        for error_level in error_levels:
            if error_level in results and bmi_group in results[error_level]:
                result = results[error_level][bmi_group]
                print(f"\t\t±{error_level:.3f}\t\t{result['平均时间']}周\t\t{result['标准差']}\t\t{result['与基准差异']:+.2f}周")
        print()

    return results

# 阈值敏感性分析
def threshold_sensitivity_analysis(df):
    print("\n检测阈值敏感性分析：")

    # 不同的阈值水平
    thresholds = [0.035, 0.04, 0.045, 0.05]
    results = {}

    for threshold in thresholds:
        print(f"\n阈值: {threshold}")

        # 修改阈值重新分析
        df_temp = df.copy()

        # 重新定义达标标准
        def modified_bmi_analysis(df, threshold_val):
            df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

            def get_bmi_group(bmi):
                if 20 <= bmi < 28:
                    return '[20,28)'
                elif 28 <= bmi < 32:
                    return '[28,32)'
                elif 32 <= bmi < 36:
                    return '[32,36)'
                elif 36 <= bmi < 40:
                    return '[36,40)'
                elif bmi >= 40:
                    return '≥40'
                else:
                    return '<20'

            df_clean = df_clean.copy()
            df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)

            # 使用新阈值
            individual_data = []
            for _, group in df_clean.groupby('孕妇代码'):
                group = group.sort_values('检测孕周')
                mask = group['Y染色体浓度'] > threshold_val  # 使用新阈值
                if mask.any():
                    optimal_time = group[mask]['检测孕周'].iloc[0]
                    达标状态 = 1
                else:
                    optimal_time = group['检测孕周'].iloc[-1]
                    达标状态 = 0

                info = group.iloc[0]
                individual_data.append({
                    'BMI组': info['BMI组'],
                    '检测时间': optimal_time,
                    '达标状态': 达标状态
                })

            individual_df = pd.DataFrame(individual_data)

            # 计算各组平均时间和达标率
            group_results = {}
            for group_name in individual_df['BMI组'].unique():
                group_data = individual_df[individual_df['BMI组'] == group_name]
                group_results[group_name] = {
                    '平均时间': round(group_data['检测时间'].mean(), 1),
                    '达标率': f"{group_data['达标状态'].mean():.2%}",
                    '样本数': len(group_data)
                }

            return group_results

        threshold_results = modified_bmi_analysis(df_temp, threshold)
        results[threshold] = threshold_results

        # 输出当前阈值结果
        for group, info in threshold_results.items():
            print(f"  {group}: {info['平均时间']}周 (达标率:{info['达标率']})")

    return results

if __name__ == '__main__':
    main()

    # 加载数据进行误差分析
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')

    # 1. 检测误差敏感性分析
    error_results = error_sensitivity_analysis(df)

    # 2. 检测阈值敏感性分析
    threshold_results = threshold_sensitivity_analysis(df)